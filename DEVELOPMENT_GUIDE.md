# ByeVape Development Guide

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager
- Modern web browser with HTTPS support

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd byevape

# Install dependencies
npm install
```

## 🔧 Development Server

### Browser Development (HTTPS)
```bash
# Start HTTPS development server (recommended)
npm run start:https

# Access the app at:
# https://localhost:3000/
```

### Standard Development
```bash
# Start standard development server
npm start

# Access the app at:
# http://localhost:3000/
```

### Why HTTPS?
- Required for Capacitor compatibility
- Enables service worker functionality
- Prevents CORS issues during development
- Matches production environment

## 📱 Capacitor Development

### Android Development
```bash
# Build the web assets
npm run build

# Sync with Capacitor
npx cap sync android

# Open in Android Studio
npx cap open android

# Or run directly on device/emulator
npx cap run android
```

### iOS Development
```bash
# Build the web assets
npm run build

# Sync with Capacitor
npx cap sync ios

# Open in Xcode
npx cap open ios
```

## 🧹 Cache Management

### Browser Cache Issues
If you're experiencing stale cache issues during development:

#### Method 1: Browser Developer Tools
1. Open Developer Tools (F12)
2. Right-click refresh button
3. Select "Empty Cache and Hard Reload"

#### Method 2: Console Commands
```javascript
// Clear app cache and reload
clearAppCache()

// Force refresh with cache bypass
forceRefresh()

// Complete app reset for testing
resetAppForTesting()
```

#### Method 3: Manual Cache Clear
1. Open browser settings
2. Clear browsing data
3. Select "Cached images and files"
4. Clear data for localhost:3000

### Capacitor Cache Issues
```bash
# Clear Capacitor cache
npx cap clean

# Rebuild and sync
npm run build
npx cap sync

# For persistent issues, delete and recreate
rm -rf android/
npx cap add android
```

## 🔍 Debugging

### Browser Console
- Open Developer Tools (F12)
- Check Console tab for detailed logs
- Look for version numbers to verify latest code is loaded

### Version Verification
```javascript
// Check current version in console
console.log(window.BYEVAPE_DEBUG)

// Should show:
// {
//   version: "2025.01.27.004",
//   buildTimestamp: "...",
//   cacheBuster: "...",
//   platform: "Browser"
// }
```

### Common Issues

#### "App not loading latest changes"
1. Use `clearAppCache()` in console
2. Hard refresh (Ctrl+Shift+R)
3. Check version number in console logs

#### "Puff tracking not working"
1. Complete onboarding first
2. Check console for errors
3. Use `resetAppForTesting()` for clean state

#### "Undo function not working"
1. Ensure puffs were logged first
2. Check action history in console
3. Verify app initialization completed

## 🏗️ Architecture Overview

### Hybrid System
The app uses a hybrid architecture combining:

1. **Modular System** (Infrastructure):
   - StateManager: Data persistence
   - UIRenderer: Basic rendering
   - TrackerLogic: Calculations
   - ErrorHandler: Error handling
   - Logger: Debugging

2. **Legacy System** (Functionality):
   - AppState class: Main logic
   - Puff tracking with action history
   - Undo functionality
   - UI updates and navigation

### Data Flow
```
Modular System (Init) → Legacy System (Control) → UI Updates
                    ↓
              Data Synchronization
```

## 🧪 Testing

### Manual Testing Workflow
1. Use `resetAppForTesting()` to start fresh
2. Complete onboarding with test data
3. Test puff logging functionality
4. Test undo functionality
5. Test progress ring and displays
6. Test page refresh persistence

### Test Data Suggestions
- **Daily Puff Count**: 100-400 (realistic range)
- **Tracking Mode**: "Session Tracking" (reduction mode)
- **Cessation Speed**: "Balanced" (60 days)

## 📦 Build & Deployment

### Web Build
```bash
# Production build
npm run build

# Preview build
npm run preview
npm run preview:https
```

### Mobile Build
```bash
# Build for mobile
npm run build

# Android
npx cap sync android
npx cap build android

# iOS
npx cap sync ios
npx cap build ios
```

## 🔧 Troubleshooting

### Development Server Won't Start
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### Capacitor Issues
```bash
# Reset Capacitor
npx cap clean
rm -rf android/ ios/
npx cap add android
npx cap add ios
```

### Persistent Cache Issues
1. Close all browser tabs
2. Clear browser data completely
3. Restart browser
4. Use incognito/private mode for testing

## 📝 Development Notes

- Always test in HTTPS mode for Capacitor compatibility
- Use browser console for debugging and version verification
- Reset app state between major testing sessions
- Check version numbers to ensure latest code is loaded
- Use the provided cache management functions liberally during development
